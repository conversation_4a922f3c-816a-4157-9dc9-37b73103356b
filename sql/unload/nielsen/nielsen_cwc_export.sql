-- Remove existing files for the day (overwrites previous exports)
REMOVE @"{database}__{env}".PUBLIC."EXCHANGE__NIELSEN__OUT__ST"/cwc_test/{dataset}/{year}/{month}/{day}/;

-- Nielsen CWC Data Export with dynamic file naming based on str_fixture_name_concat
-- Note: This template will need to be executed once per unique str_fixture_name_concat value
-- The DAG should query for distinct fixture names and run this for each one
COPY INTO @"{database}__{env}".PUBLIC."EXCHANGE__NIELSEN__OUT__ST"/cwc_test/{dataset}/{year}/{month}/{day}/{{params.fixture_filename}}.csv
FROM "{database}__{env}"."{schema}"."{{params.table}}"
WHERE "str_fixture_name_concat" = '{{params.fixture_name}}'
SINGLE = TRUE
HEADER = TRUE
MAX_FILE_SIZE=1000000000
OVERWRITE = TRUE;
