-- Remove existing files for the day (overwrites previous exports)
REMOVE @"{database}__{env}".PUBLIC."EXCHANGE__NIELSEN__OUT__ST"/cwc_test/{dataset}/{year}/{month}/{day}/;

-- Nielsen CWC Data Export using PARTITION BY
COPY INTO @"{database}__{env}".PUBLIC."EXCHANGE__NIELSEN__OUT__ST"/cwc_test/{dataset}/{year}/{month}/{day}/
FROM "{database}__{env}"."{schema}"."{{params.table}}"
PARTITION BY ("str_fixture_name_concat")
HEADER = TRUE
MAX_FILE_SIZE=1000000000;
