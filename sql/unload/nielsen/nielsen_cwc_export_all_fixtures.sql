-- Remove existing files for the day (overwrites previous exports)
REMOVE @"{database}__{env}".PUBLIC."EXCHANGE__NIELSEN__OUT__ST"/cwc_test/{dataset}/{year}/{month}/{day}/;

-- Nielsen CWC Data Export with dynamic file naming for all fixtures
-- This script creates separate files for each fixture using a JavaScript stored procedure

-- Create a JavaScript stored procedure to handle dynamic file exports
CREATE OR REPLACE PROCEDURE export_nielsen_fixtures(
    database_env STRING,
    schema_name STRING,
    table_name STRING,
    dataset STRING,
    year STRING,
    month STRING,
    day STRING
)
RETURNS STRING
LANGUAGE JAVASCRIPT
AS
$$
    // Get distinct fixture names
    var get_fixtures_sql = `
        SELECT DISTINCT
            "str_fixture_name_concat" as fixture_name,
            REGEXP_REPLACE(
                REGEXP_REPLACE("str_fixture_name_concat", '[^a-zA-Z0-9_-]', '_'),
                '_+', '_'
            ) as fixture_filename
        FROM "${DATABASE_ENV}"."${SCHEMA_NAME}"."${TABLE_NAME}"
    `;

    var stmt = snowflake.createStatement({sqlText: get_fixtures_sql});
    var result_set = stmt.execute();

    var export_count = 0;

    // Loop through each fixture and create a separate export
    while (result_set.next()) {
        var fixture_name = result_set.getColumnValue(1);
        var fixture_filename = result_set.getColumnValue(2);

        // Create the export SQL for this specific fixture
        var export_sql = `
            COPY INTO @"${DATABASE_ENV}".PUBLIC."EXCHANGE__NIELSEN__OUT__ST"/cwc_test/${DATASET}/${YEAR}/${MONTH}/${DAY}/${fixture_filename}.csv
            FROM "${DATABASE_ENV}"."${SCHEMA_NAME}"."${TABLE_NAME}"
            WHERE "str_fixture_name_concat" = '${fixture_name}'
            SINGLE = TRUE
            HEADER = TRUE
            MAX_FILE_SIZE=1000000000
            OVERWRITE = TRUE
        `;

        // Execute the export
        var export_stmt = snowflake.createStatement({sqlText: export_sql});
        export_stmt.execute();
        export_count++;
    }

    return `Successfully exported ${export_count} fixture files`;
$$;

-- Execute the stored procedure
CALL export_nielsen_fixtures(
    '{database}__{env}',
    '{schema}',
    '{{params.table}}',
    '{dataset}',
    '{year}',
    '{month}',
    '{day}'
);

-- Clean up the stored procedure
DROP PROCEDURE export_nielsen_fixtures(STRING, STRING, STRING, STRING, STRING, STRING, STRING);
