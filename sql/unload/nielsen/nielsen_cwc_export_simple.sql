-- Remove existing files for the day (overwrites previous exports)
REMOVE @"{database}__{env}".PUBLIC."EXCHANGE__NIELSEN__OUT__ST"/cwc_test/{dataset}/{year}/{month}/{day}/;

-- Nielsen CWC Data Export with dynamic file naming for all fixtures
-- This approach uses PARTITION BY with a cleaned filename column
-- Each partition creates a directory, but we can work with this structure

COPY INTO @"{database}__{env}".PUBLIC."EXCHANGE__NIELSEN__OUT__ST"/cwc_test/{dataset}/{year}/{month}/{day}/
FROM (
    SELECT *,
           -- Create a cleaned filename-safe version of the fixture name
           -- This will be used as the partition key
           REGEXP_REPLACE(
               REGEXP_REPLACE("str_fixture_name_concat", '[^a-zA-Z0-9_-]', '_'),
               '_+', '_'
           ) AS filename_safe_fixture_name
    FROM "{database}__{env}"."{schema}"."{{params.table}}"
)
PARTITION BY (filename_safe_fixture_name)
HEADER = TRUE
MAX_FILE_SIZE=1000000000;
