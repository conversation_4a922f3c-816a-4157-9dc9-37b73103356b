-- current_timestamp() used below is not the snowflake function, it refers to a user_defined_macro defined in Airflow as part of our EDMDAG
-- If the Path in the COPY INTO is updated, please also update the Path in the ALTER EXTERNAL TABLE
COPY INTO @"{database}__{env}"."BASE"."{{params.stage}}"/{dataset}/{year}/{month}/{day}/{{ task.params["audit_process_id"] }}/{{ task.params["audit_task_id"] }}/{{ current_timestamp() }}/{dataset}
    FROM "{database}__{env}"."{schema}"."{{params.table}}"
HEADER = TRUE;

USE ROLE "EXCHANGE__BASE__{env}__ROLE";
ALTER EXTERNAL TABLE IF EXISTS "{database}__{env}"."BASE"."{{params.external_table}}" REFRESH '{year}/{month}/{day}/{{ task.params["audit_process_id"] }}/{{ task.params["audit_task_id"] }}/';
