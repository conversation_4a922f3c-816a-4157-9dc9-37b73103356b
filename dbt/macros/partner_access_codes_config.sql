
{% macro partner_access_codes_config() %}

{% set yml_str %}

skyde: {
    campaign_filter: ['dazn x sky de linear channel 2021', 'dazn x sky de linear channel annual 2023 unlimited', 'dazn x sky de linear channel monthly 2023 standard', 'dazn x sky at linear channel 2023', 'dazn x skydemo22', 'dazn x sky at linear channel annual 2023', 'dazn x sky de linear channel annual 2023 standard', 'dazn x sky de linear channel monthly 2023 unlimited', 'dazn x sky de linear channel 2023 standard'],
    skip_code_gen: 'True'
}

povo: {
    campaign_filter: ['dazn x povo'],
    skip_code_gen: 'True'
}

kddi: {
    campaign_filter: ['dazn x kddi', 'dazn x kddi bundle patched', 'dazn x kddi eft patched'],
    skip_code_gen: 'True'
}

telefonica: {
    campaign_filter: ['dazn x telefonica bundle', 'dazn x telefonica a la carte', 'dazn x ms-bd', 'dazn x ms-ac', 'dazn x ll-bd', 'dazn x ll-ac', 'dazn x ll-bd-ms-bd', 'dazn x ll-ac-ms-ac', 'dazn x ll-ac-ms-bd', 'dazn x ll-bd-ms-ac', 'test x telephonica test campaign'],
    skip_code_gen: 'True'
}

vodafonegermany: {
    campaign_filter: ['dazn x vodafone de lc bundle', 'dazn x vodafone de lc bundle unity media', 'dazn x vodafone de linear channel 2023 standard', 'dazn x vodafone unity media de linear channel 2023 standard'],
    skip_code_gen: 'True'
}

vodafoneitaly: {
    campaign_filter: ['dazn x vodafone it 6m', 'dazn x vodafone it standalone', 'dazn x vodafone it 3m', 'dazn x vodafone it 3m mobile', 'dazn x vodafone it 3m cc', 'dazn x vodafone it 6m cc'],
    skip_code_gen: 'True'
}

ctc: {
    campaign_filter: ['dazn x ctc'],
    skip_code_gen: 'True'
}

euskaltel: {
    campaign_filter: ['dazn x bundle euskaltel', 'dazn x alc euskaltel', 'dazn x euskaltel multisport bundle', 'dazn x euskaltel allsports bundle', 'dazn x euskaltel alc', 'dazn x euskaltel bundle', 'dazn x euskaltel total bundle 2023', 'dazn x euskaltel esencial bundle 2023'],
    skip_code_gen: 'True'
}

masmovil: {
    campaign_filter: ['dazn x masmovil 3 months', 'dazn x yoigo multisport bundle', 'dazn x yoigo allsports bundle','dazn x masmovil total 2023','dazn x masmovil esencial 2023', 'dazn x yoigo total bundle 2023', 'dazn x yoigo esencial bundle 2023'],
    skip_code_gen: 'True'
}

orange: {
    campaign_filter: ['dazn x sp bundle orange', 'dazn x osp futbol', 'dazn x osp total', 'dazn x orange test jan 2024', 'dazn x orange sp bundle'],
    skip_code_gen: 'True'
}

deutschetelekom: {
    campaign_filter: ['dazn x deutsche telekom de bundle 1', 'dazn x deutsche telekom de bundle 2', 'dazn x dt magenta monthly 2023 standard', 'dazn x dt magenta annual 2023 standard', 'dazn x dt festnetz monthly 2023 standard', 'dazn x dt festnetz annual 2023 standard', 'dazn x dt magenta monthly 2023 unlimited', 'dazn x dt magenta annual 2023 unlimited', 'dazn x dt festnetz annual 2023 unlimited', 'dazn x dt festnetz monthly 2023 standard', 'dazn x dt festnetz monthly 2023 unlimited', 'dazn x dt new festnetz monthly 2023 standard'],
    skip_code_gen: 'True'
}

avatel: {
    campaign_filter: ['dazn x avatel esencial', 'dazn x avatel total'],
    skip_code_gen: 'True'
}

tim: {
    campaign_filter: ['dazn x tim 2021 alc fixed', 'dazn x tim 2021 alc mobile', 'dazn x tim 2021 bundle fixed', 'dazn x tim 2021 bundle mobile', 'dazn x tim landline standard', 'dazn x tim mobile standard', 'dazn x tim landline plus', 'dazn x tim mobile plus', 'dazn x tim 2021 bundle olo', 'dazn x tim 2021 alc olo', 'dazn x tim olo standard', 'dazn x tim olo plus', 'dazn x tim mobile goal', 'dazn x tim olo goal', 'dazn x tim landline goal', 'dazn x dazn x tim mobile goal', 'test x pac tim test campaign for feature tiering silver', 'test x pac tim test campaign for feature tiering gold'],
    skip_code_gen: 'True'
}

skymas: {
    campaign_filter: ['dazn x sky mas monthly uk', 'dazn x sky mas annual uk', 'dazn x sky mas contract uk', 'dazn x sky mas monthly ie', 'dazn x sky mas annual ie', 'dazn x sky mas contract ie'],
    skip_code_gen: 'True'
}

vodafonejapan: {
    campaign_filter: ['dazn x dmm'],
    skip_code_gen: 'True'
}

jcta: {
    campaign_filter: ['dazn x jcta 2019', 'dazn x jcta sales staff', 'dazn x jcta 2019dazn x epay 4251404565404 mgefj', 'dazn x jcta reps 2 month'],
    skip_code_gen: 'True'
}

jcom: {
    campaign_filter: ['dazn x jcom 2019'],
    skip_code_gen: 'True'
}

{% endset %}
{{ return(fromyaml(yml_str)) }}
{% endmacro %}
