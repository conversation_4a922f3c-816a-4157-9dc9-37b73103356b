{{
    config(
        materialized='table',
        database='EXCHANGE__PARTNER_ACCESS_CODES__OUT__' + snowflake_env(),
        schema='REPORT',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
        tags=['exchange-partner-access-codes-daily'],
        query_tag='exchange_partner_access_codes'
    )
}}

{% set partner_config = partner_access_codes_config()['jcta'] %}


SELECT DISTINCT
    "subscription_sign_up_giftcode" AS "code"
    ,TO_CHAR("min_billing_account_subscription_name_created_date",'yyyy/mm/dd') AS "first sign up date"
    ,TO_VARCHAR(TO_DATE('{{ var('batch_date') }}','yyyy-mm-dd'),'yyyy/mm/dd') AS "last sign up date"
    ,'N' AS "free trial"
    ,1 AS "product code"
    ,CASE
        WHEN TO_DATE('{{ var('batch_date') }}','yyyy-mm-dd') >= '2024-04-01' THEN 4200
        ELSE 3700
    END AS "price"
FROM {{ ref('partner_access_codes_base') }}
WHERE "subscription_sign_up_campaign_id" ILIKE ANY ({{ in_clause_from_list(partner_config['campaign_filter']) }})
AND DATE_TRUNC(DAY, "subscription_id_created_timestamp") = '{{ var('batch_date') }}'
