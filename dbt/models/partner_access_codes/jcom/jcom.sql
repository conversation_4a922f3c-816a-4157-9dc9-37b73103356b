{{
    config(
        materialized='table',
        database='EXCHANGE__PARTNER_ACCESS_CODES__OUT__' + snowflake_env(),
        schema='REPORT',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
        tags=['exchange-partner-access-codes-daily'],
        query_tag='exchange_partner_access_codes'
    )
}}

{% set partner_config = partner_access_codes_config()['jcom'] %}


WITH subs_filtered AS (
    SELECT * FROM {{ source('SUBSCRIPTION_MART_INTERMEDIATE','DAILY_SUBSCRIPTIONS_FILTERED') }}
)

,subscription_source AS (
    SELECT * FROM {{ source('SUBSCRIPTION_DOMAIN','SUBSCRIPTION_ID__DIM') }}
)

SELECT
    subscription_source."subscription_sign_up_giftcode" AS "code"
    ,TO_CHAR(subscription_source."subscription_start_date",'yyyy/mm/dd') AS "subscription start date"
    ,'N' AS "ft"
FROM subs_filtered
LEFT JOIN subscription_source
    ON subs_filtered."subscription_id" = subscription_source."subscription_id"
WHERE subscription_source."subscription_sign_up_giftcode" IS NOT NULL
AND subs_filtered."subscription_sign_up_campaign_id" ILIKE ANY ({{ in_clause_from_list(partner_config['campaign_filter']) }})
AND subscription_source."subscription_sign_up_giftcode" NOT LIKE '%PRTEST%'
AND subs_filtered."batch_date" = '{{ var('batch_date') }}'
