{{
    config(
        materialized='table',
        database='EXCHANGE__PARTNER_ACCESS_CODES__OUT__' + snowflake_env(),
        schema='TRANSIENT',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
        tags=['exchange-partner-access-codes-daily'],
        query_tag='exchange_partner_access_codes'
    )
}}

{% set partner_config = partner_access_codes_config()['vodafonegermany'] %}

SELECT DISTINCT
    "subscription_sign_up_giftcode" AS "code"
    ,"subscription_sign_up_campaign_id"
    ,"subscription_id_created_timestamp"::DATE AS "applied on"
FROM {{ ref('partner_access_codes_base') }}
WHERE "subscription_sign_up_campaign_id" ILIKE ANY ({{ in_clause_from_list(partner_config['campaign_filter']) }})
AND DATE_TRUNC(DAY, "subscription_id_created_timestamp") >= '2020-11-18'
AND DATE_TRUNC(DAY, "subscription_id_created_timestamp") < '{{ var('batch_date') }}'
