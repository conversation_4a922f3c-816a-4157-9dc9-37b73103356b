{{
    config(
        materialized='table',
        database='EXCHANGE__PARTNER_ACCESS_CODES__OUT__' + snowflake_env(),
        schema='REPORT',
        tags=['exchange-partner-access-codes-monthly'],
        query_tag='exchange_partner_access_codes'
    )
}}

{% set partner_config = partner_access_codes_config()['tim'] %}

WITH tim_camp_subs AS (
    SELECT * FROM {{ source('SUBSCRIPTION_DOMAIN','SUBSCRIPTION_NAME__SCD') }}
    -- Only TIM campaigns
    WHERE "subscription_sign_up_campaign_id" ILIKE ANY ({{ in_clause_from_list(partner_config['campaign_filter']) }})
)

,tier_changes AS (
    SELECT
        "subscription_name"
        ,"record_valid_from_timestamp" AS "subscription_tier_change_timestamp"
        ,"subscription_tier" AS "current_subscription_tier"
        ,LAG("subscription_tier") OVER (PARTITION BY "subscription_name" ORDER BY "record_valid_from_timestamp") AS "previous_subscription_tier"
    FROM tim_camp_subs
    -- Only lines where the row before was a different tier
    QUALIFY "previous_subscription_tier" != "subscription_tier"
)

SELECT
    '{{ var('data_interval_end_date') }}' AS "batch_date"
    ,tim_camp_subs."subscription_name"
    ,tim_camp_subs."subscription_name_original_created_timestamp"::TIMESTAMP_NTZ AS "subscription_name_original_created_timestamp"
    ,tim_camp_subs."subscription_tier"
    ,tim_camp_subs."subscription_sign_up_giftcode"
    ,tim_camp_subs."subscription_sign_up_campaign_id"
    ,tier_changes."subscription_tier_change_timestamp"::TIMESTAMP_NTZ AS "subscription_tier_change_timestamp"
FROM tim_camp_subs
LEFT JOIN tier_changes
    ON tim_camp_subs."subscription_name" = tier_changes."subscription_name"
    -- Only get tier changes from before the batch_date
    AND tier_changes."subscription_tier_change_timestamp"::DATE < '{{ var('data_interval_end_date') }}'
WHERE
    -- Get active subs on the batch_date
    tim_camp_subs."record_valid_from_timestamp"::DATE <= DATEADD(DAY, -1, '{{ var('data_interval_end_date') }}')
    AND
    tim_camp_subs."record_valid_until_timestamp"::DATE > DATEADD(DAY, -1, '{{ var('data_interval_end_date') }}')
    AND
    -- That aren't Batch50/test acccounts
    "billing_account_is_batch_50" = FALSE
    AND
    -- And not draft
    "is_draft" = FALSE
-- Taking the most recent tier change to that batch_date
QUALIFY ROW_NUMBER() OVER (PARTITION BY tim_camp_subs."subscription_name" ORDER BY DATEDIFF('day', tier_changes."subscription_tier_change_timestamp", "batch_date")) = 1
