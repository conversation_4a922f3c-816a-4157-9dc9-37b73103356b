{{
config(
    materialized='table',
    database='EXCHANGE__NIELSEN__OUT__' + snowflake_env(),
    schema='NIELSEN_CWC',
    tags=['exchange-nielsen-cwc'],
    snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_M_WH'
)
}}

/* This model uses the output of the nielsen_cwc_intermediate view to count the number concurrent streams
by device. We use the seq4 function to create a sequence of 1 minute time intervals and count the number of streams by device
type during those time periods.
*/

WITH concurrency AS (
    SELECT * FROM {{ ref('nielsen_cwc_intermediate') }}
)

, interval_sequence AS (
    SELECT
        DATEADD(MINUTE, SEQ4(), TO_DATE('{{ var('batch_date') }}', 'YYYY-MM-DD')) AS "time_interval"
    FROM TABLE(GENERATOR(ROWCOUNT => 1440))  -- Generate one day of rows
    WHERE "time_interval" < DATEADD(DAY, 1, TO_DATE('{{ var('batch_date') }}', 'YYYY-MM-DD'))
    ORDER BY 1
)

, count_streams AS (
    SELECT
        interval_sequence."time_interval" AS "time_interval"
        ,concurrency."str_country" AS "str_country"
        ,concurrency."str_territory" AS "str_territory"
        ,concurrency."str_fixture_name_concat" AS "str_fixture_name_concat"
        ,concurrency."str_competition_name_concat" AS "str_competition_name_concat"
        ,concurrency."str_sport_name" AS "str_sport_name"
        ,concurrency."str_fixture_date" AS "str_fixture_date"
        ,concurrency."device_partition" AS "device_partition"
        ,COUNT(*) AS "ttl_streams"
    FROM interval_sequence
    LEFT JOIN concurrency
        ON interval_sequence."time_interval" >= concurrency."playback_start_timestamp"
        AND interval_sequence."time_interval" < concurrency."playback_end_timestamp"
    WHERE DATE_TRUNC(DAY, concurrency."str_fixture_date") = TO_DATE('{{ var('batch_date') }}', 'YYYY-MM-DD')
    GROUP BY 1, 2, 3, 4, 5, 6, 7, 8
    ORDER BY 1
)

SELECT
    count_streams."time_interval"
    ,count_streams."str_territory"
    ,count_streams."str_country"
    ,count_streams."str_fixture_name_concat"
    ,count_streams."str_competition_name_concat"
    ,count_streams."str_sport_name"
    ,count_streams."str_fixture_date"
    ,SUM(CASE WHEN count_streams."device_partition" = 'Mobile' THEN count_streams."ttl_streams" END) AS "mobile_ttl_streams_sum"
    ,SUM(CASE WHEN count_streams."device_partition" = 'Living Room' THEN count_streams."ttl_streams" END) AS "living_room_ttl_streams_sum"
    ,SUM(CASE WHEN count_streams."device_partition" = 'Web' THEN count_streams."ttl_streams" END) AS "web_ttl_streams_sum"
    ,SUM(CASE WHEN count_streams."device_partition" = 'Others' THEN count_streams."ttl_streams" END) AS "others_ttl_streams_sum"
FROM count_streams
GROUP BY 1, 2, 3, 4, 5, 6, 7
ORDER BY 1
