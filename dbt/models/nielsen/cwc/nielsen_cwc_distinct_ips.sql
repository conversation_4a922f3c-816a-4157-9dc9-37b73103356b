{{
config(
    materialized='table',
    database='EXCHANGE__NIELSEN__OUT__' + snowflake_env(),
    schema='NIELSEN_CWC',
    tags=['exchange-nielsen-cwc'],
    snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_M_WH'
)
}}

/* This model uses the output of the nielsen_cwc_intermediate view to count the number of distinct IP addresses
by device type for competition ID dc4k1xh2984zbypbnunk7ncic across all territories.
*/

WITH distinct_ips AS (
    SELECT * FROM {{ ref('nielsen_cwc_intermediate') }}
)

, count_ips AS (
    SELECT
        "str_country" AS "str_country"
        ,"str_territory" AS "str_territory"
        ,"str_fixture_name_concat" AS "str_fixture_name_concat"
        ,"str_competition_name_concat" AS "str_competition_name_concat"
        ,"str_sport_name" AS "str_sport_name"
        ,"str_fixture_date" AS "str_fixture_date"
        ,"device_partition" AS "device_partition"
        ,COUNT(DISTINCT "ip_address_hash") AS "distinct_count_ip_address"
    FROM distinct_ips
    GROUP BY 1, 2, 3, 4, 5, 6, 7
)

SELECT
    count_ips."str_territory"
    ,count_ips."str_country"
    ,count_ips."str_fixture_name_concat"
    ,count_ips."str_competition_name_concat"
    ,count_ips."str_sport_name"
    ,count_ips."str_fixture_date"
    ,SUM(CASE WHEN count_ips."device_partition" = 'Mobile' THEN count_ips."distinct_count_ip_address" END) AS "mobile_distinct_count_ip_address_sum"
    ,SUM(CASE WHEN count_ips."device_partition" = 'Living Room' THEN count_ips."distinct_count_ip_address" END) AS "living_room_distinct_count_ip_address_sum"
    ,SUM(CASE WHEN count_ips."device_partition" = 'Web' THEN count_ips."distinct_count_ip_address" END) AS "web_distinct_count_ip_address_sum"
    ,SUM(CASE WHEN count_ips."device_partition" = 'Others' THEN count_ips."distinct_count_ip_address" END) AS "others_distinct_count_ip_address_sum"
FROM count_ips
GROUP BY 1, 2, 3, 4, 5, 6
ORDER BY 1
