version: 2

models:
  - name: nielsen_cwc_key_moments
    description: "Table exporting Key Moments information to Nielsen for CWC Audience Measurement"
    columns:
      - name: INSERT_DATE
        description: "Date the pipeline ran"
        quote: true

      - name: BATCH_DATE
        description: "Date the fixture is scheduled"
        quote: true

      - name: FIXTURE_ID
        description: "ID of the match"
        quote: true

      - name: FIXTURE_NAME
        description: "Teams that are involved in said match"
        quote: true

      - name: COMPETITION_NAME
        description: "Name of the competition"
        quote: true

      - name: KICK_OFF_EPOCH
        description: "Time of Kick off measured in unix time"
        quote: true

      - name: FIRST_HALF_END_EPOCH
        description: "Time that First Half ends measured in unix time"
        quote: true

      - name: SECOND_HALF_START_EPOCH
        description: "Time Second half starts measured in unix time"
        quote: true

      - name: END_OF_MATCH_EPOCH
        description: "Time match ends measured in unix time"
        quote: true

      - name: FEED_START_EPOCH
        description: "Time feed starts measured in unix time"
        quote: true

      - name: FEED_END_EPOCH
        description: "Time feed ends measured in unix time"
        quote: true
