{{
config(
    materialized='table',
    tags=['exchange-nielsen-cwc'],
    database='EXCHANGE__NIELSEN__OUT__' + snowflake_env(),
    schema='NIELSEN_CWC',
    snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH'
)
}}

WITH key_moments_fact AS (
    SELECT * FROM {{ source_env('ANALYTICS','key_moments_fact') }}
)

SELECT
    CURRENT_DATE AS INSERT_DATE
    ,"batch_date" AS BATCH_DATE
    -- ,"fixture_id" AS FIXTURE_ID -- Removed as requested
    -- ,"fixture_name" AS FIXTURE_NAME
    ,"competition_name" AS COMPETITION_NAME
    -- Add standardized fixture name format for consistency across models
    ,IFNULL("fixture_name", '') || '-' || IFNULL(TO_CHAR(TO_TIMESTAMP_NTZ("kick_off_epoch"/1000), 'YYYY-MM-DDTHH24:MI:SS'), '') AS "str_fixture_name_concat"
    ,"kick_off_epoch" AS KICK_OFF_EPOCH
    ,"first_half_epoch" AS FIRST_HALF_END_EPOCH
    ,"second_half_epoch" AS SECOND_HALF_START_EPOCH
    ,"end_of_match_epoch" AS END_OF_MATCH_EPOCH
    ,"feed_start_epoch" AS FEED_START_EPOCH
    ,"feed_end_epoch" AS FEED_END_EPOCH
FROM key_moments_fact
WHERE 1=1
    {# AND "fixture_id" IN (
        SELECT DISTINCT "fixture_id"
        FROM {{ source('CONTENT','CONTENT_ITEM__DIM') }}
        WHERE "competition_id" = 'dc4k1xh2984zbypbnunk7ncic'
    ) #}
    AND "competition_name" = 'Serie A' AND "outlet" = 'italy'
    AND "batch_date" = TO_DATE('{{ var('batch_date') }}', 'YYYY-MM-DD')
