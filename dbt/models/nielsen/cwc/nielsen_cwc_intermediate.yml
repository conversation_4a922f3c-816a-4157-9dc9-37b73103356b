version: 2

models:
  - name: nielsen_cwc_intermediate
    description: "Intermediate model for Nielsen CWC reporting. Filters for competition ID dc4k1xh2984zbypbnunk7ncic across all territories."
    columns:
      - name: str_country
        description: "Stream country (from playback_stream_country)"
        quote: true

      - name: str_territory
        description: "Stream territory (from playback_stream_state)"
        quote: true

      - name: str_fixture_name_concat
        description: "Concatenated fixture name"
        quote: true

      - name: str_competition_name_concat
        description: "Concatenated competition name"
        quote: true

      - name: str_sport_name
        description: "Sport name"
        quote: true

      - name: str_fixture_date
        description: "Fixture date"
        quote: true

      - name: device_hardware_type
        description: "Device hardware type"
        quote: true

      - name: device_partition
        description: "Combined device category derived from application_category and device_hardware_type: mobile (Mobile Phone, Tablet), living room (TV, Games Console, Set Top Box), web (Desktop), or others"
        quote: true

      - name: playback_start_timestamp
        description: "Playback start timestamp"
        quote: true

      - name: playback_end_timestamp
        description: "Playback end timestamp"
        quote: true

      - name: ip_address_hash
        description: "IP address hash"
        quote: true
