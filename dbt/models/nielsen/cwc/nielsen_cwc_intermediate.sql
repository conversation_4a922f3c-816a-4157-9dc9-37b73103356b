{{
config(
    materialized='view',
    database='EXCHANGE__NIELSEN__OUT__' + snowflake_env(),
    schema='TRANSIENT',
    tags=['exchange-nielsen-cwc'],
    snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_M_WH'
)
}}

/* This dbt model is part of the Nielsen CWC reporting.
This intermediate view joins data from PlaysMart and selects the columns necessary for the distinct IP count and concurrency models.
We filter using the competition id dc4k1xh2984zbypbnunk7ncic for all territories.
*/

WITH playback_stream_fact AS (
    SELECT * FROM {{ source_env('FACT','PLAYBACK_STREAM__FACT') }}
)

, content_dim AS (
    SELECT * FROM PLAYBACK_STREAM__B2C__MART__PROD.CONTENT."CONTENT_ITEM__DIM"
)

, location_dim AS (
    SELECT * FROM {{ source_env('GLOBAL','PLAYBACK_STREAM_LOCATION__DIM') }}
)

, device_dim AS (
    SELECT * FROM {{ source_env('OPERATIONS','DEVICE_INFO__DIM') }}
)

, application_dim AS (
    SELECT * FROM {{ source_env('OPERATIONS','APPLICATION__DIM') }}
)

, stream_details_dim AS (
    SELECT * FROM {{ source_env('PLAYBACK_STREAM','PLAYBACK_STREAM_DETAILS__DIM') }}
)


, streams AS (
    SELECT
        location_dim."playback_stream_country" AS "str_country"
        ,location_dim."playback_stream_territory" AS "str_territory"
        ,IFNULL(content_dim."article_type", '') || '-' || IFNULL(content_dim."fixture_name", '') || '-' || IFNULL(TO_CHAR(content_dim."fixture_start_timestamp", 'YYYY-MM-DDTHH24:MI:SS'), '') AS "str_fixture_name_concat"
        -- date format required by Nielsen
        ,IFNULL(content_dim."competition_country",'') || '-' || content_dim."competition_name" AS "str_competition_name_concat"
        ,content_dim."sport_name" AS "str_sport_name"
        ,content_dim."fixture_start_timestamp" AS "str_fixture_date"
        {# ,device_dim."device_hardware_type" #}
        {# ,CASE
            WHEN device_dim."device_hardware_type" IN ('Mobile Phone', 'Tablet') OR device_dim."device_category" = 'Mobile' THEN 'mobile'
            WHEN device_dim."device_hardware_type" IN ('TV', 'Games Console', 'Set Top Box') THEN 'living room'
            WHEN device_dim."device_hardware_type" IN ('Desktop') THEN 'web'
            ELSE 'others'
         END AS "device_partition" #}
        ,application_dim."application_category" AS "device_partition"
        ,playback_stream_fact."playback_start_timestamp"
        ,playback_stream_fact."playback_end_timestamp"
        ,playback_stream_fact."ip_address_hash"
    FROM playback_stream_fact
    LEFT JOIN content_dim ON playback_stream_fact."content_item__skey" = content_dim."content_item__skey"
    LEFT JOIN location_dim ON playback_stream_fact."playback_stream_location__skey" = location_dim."playback_stream_location__skey"
    LEFT JOIN application_dim ON playback_stream_fact."application__skey" = application_dim."application__skey"
    {# LEFT JOIN device_dim ON playback_stream_fact."device_info__skey" = device_dim."device_info__skey" #}
    LEFT JOIN stream_details_dim ON playback_stream_fact."playback_stream_details__skey" = stream_details_dim."playback_stream_details__skey"
    WHERE
        content_dim."article_type" = 'Live'
        AND DATE_TRUNC(DAY, playback_stream_fact."playback_stream_date") >= TO_DATE('{{ var('batch_date') }}', 'YYYY-MM-DD')
        AND DATE_TRUNC(DAY, content_dim."fixture_start_timestamp") = TO_DATE('{{ var('batch_date') }}', 'YYYY-MM-DD')
        AND "playback_duration_milliseconds" > 0
        AND "is_engaged_play" = 1
        {# AND content_dim."competition_id" = 'dc4k1xh2984zbypbnunk7ncic' #}
        AND "competition_name" = 'Serie A'
)

SELECT * FROM streams
