version: 2

models:
  - name: nielsen_cwc_distinct_ips
    description: "Distinct IPs model for Nielsen CWC reporting. Counts distinct IP addresses by device category for competition ID dc4k1xh2984zbypbnunk7ncic across all territories."
    columns:
      - name: str_territory
        description: "Stream territory (from playback_stream_state)"
        quote: true

      - name: str_country
        description: "Stream country (from playback_stream_country)"
        quote: true

      - name: str_fixture_name_concat
        description: "Concatenated fixture name"
        quote: true

      - name: str_competition_name_concat
        description: "Concatenated competition name"
        quote: true

      - name: str_sport_name
        description: "Sport name"
        quote: true

      - name: str_fixture_date
        description: "Fixture date"
        quote: true

      - name: mobile_distinct_count_ip_address_sum
        description: "Distinct IP count for mobile devices (Mobile Phone, Tablet)"
        quote: true

      - name: living_room_distinct_count_ip_address_sum
        description: "Distinct IP count for living room devices (TV, Games Console, Set Top Box)"
        quote: true

      - name: web_distinct_count_ip_address_sum
        description: "Distinct IP count for web devices (Desktop)"
        quote: true

      - name: others_distinct_count_ip_address_sum
        description: "Distinct IP count for other devices"
        quote: true
