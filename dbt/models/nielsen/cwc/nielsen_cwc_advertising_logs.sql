{{
config(
    materialized='table',
    database='EXCHANGE__NIELSEN__OUT__' + snowflake_env(),
    schema='NIELSEN_CWC',
    tags=['exchange-nielsen-cwc'],
    snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH'
)
}}

/*
    This table prepares the advertising logs data required for Nielsen CWC.
    We collect all commercial and sponsorship logs and enrich them with content information.
    This data is then exported to Nielsen with one file per fixture.
*/

WITH logs AS (
    SELECT * FROM {{ source_env('ADVERTISING_FACT', 'ADVERTISING_LOGS') }}
)

,content AS (
    SELECT * FROM {{ source_env('CONTENT_DIMENSION', 'MFL_FIXTURE__DIM') }}
)

,fixture_data AS (
    SELECT
        date_trunc(MINUTE, logs. "ad_start_timestamp") AS "Timestamp"
        ,logs."ad_date"
        ,logs."territory" AS "str_territory"
        ,logs."ad_start_timestamp"
        ,logs."ad_end_timestamp"
        ,logs."creative"
        ,logs."line_item_name"
        ,logs."ad_type"
        ,logs."delivery_type"
        ,logs."source"
        ,logs."channel"
        ,logs."advertiser"
        ,logs."pl"
        ,REPLACE(SPLIT_PART("creative", '-', -1) ,'_1080p', '') AS "external_id"
        ,logs."campaign"
        ,logs."fixture_id"
        ,content."fixture_description" AS "fixture_name"
        ,content."fixture_start_timestamp" AS "str_fixture_date"
        ,content."competition_name"
        ,content."competition_id"
        ,logs."primary_key"
        -- Create standardized fixture name format for consistency across models
        ,IFNULL(content."fixture_description", '') || '-' || IFNULL(TO_CHAR(content."fixture_start_timestamp", 'YYYY-MM-DDTHH24:MI:SS'), '') AS "str_fixture_name_concat"
        -- Add competition name concat for consistency with other models
        ,IFNULL(content."competition_country_name",'') || '-' || content."competition_name" AS "str_competition_name_concat"
        -- Add sport name for consistency with other models
        ,content."sport_name" AS "str_sport_name"
    FROM logs
    LEFT JOIN content
        ON content."fixture_id" = logs."fixture_id"
    WHERE logs."ad_date" = TO_DATE('{{ var('batch_date') }}', 'YYYY-MM-DD')
    AND COALESCE(logs."campaign",'none') != 'unknown'
    AND logs."ad_type" IN ('Commercial','Sponsorship')
    -- Filter for Serie A matches
    AND content."competition_name" = 'Serie A'
)

SELECT
    "Timestamp"
    ,"ad_date"
    ,"ad_start_timestamp"
    ,"ad_end_timestamp"
    ,"advertiser"
    ,"creative"
    ,"line_item_name"
    ,"pl"
    ,"external_id"
    ,"campaign"
    ,"fixture_id"
    ,"ad_type"
    ,"delivery_type"
    ,"str_fixture_name_concat"
    ,"str_competition_name_concat"
    ,"str_territory"
    ,"str_sport_name"
FROM fixture_data