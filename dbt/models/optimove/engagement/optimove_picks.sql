{{
    config(
        materialized='table',
        transient=false,
        database='EXCHANGE__OPTIMOVE__OUT__' + snowflake_env(),
        schema='ENGAGEMENT',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
        tags=['exchange-picks'],
        alias='picks'
    )
}}

-- {% set categories_list = [ 'j_league', 'npb', 'f1', 'eu_football' ] %}
WITH picks_source_data_raw AS (
    SELECT * FROM {{ source('STAGING_PICKS', 'STAGING__DAZN_PICKS_JAPAN_USER_POINTS') }}
    WHERE TRUE
        -- AND "category_name" IN ({{ in_clause_from_list(categories_list) }})
    GROUP BY ALL
)
-- ,category_name_mapping AS (
--     SELECT *
--     FROM (
--         VALUES
--             ('Ｊリーグクイズ2024', 'j_league')
--             , ('プロ野球クイズ2024', 'npb')
--             , ('野球クイズ2024', 'npb')
--             , ('F1®クイズ2024', 'f1')
--             , ('欧州サッカークイズ2024', 'eu_football')
--         ) AS t ("JAPANESE_CATEGORY_NAME", "ENGLISH_CATEGORY_NAME")
-- )
    -- to do:: join with the source and change the category name accrodingly 
    --ENGLISH_CATEGORY_NAME AS "category_name"
    -- INNER JOIN category_name_mapping
    -- ON source."CATEGORY_NAME" = category_name_mapping."JAPANESE_CATEGORY_NAME"

,picks_source_data AS (
    SELECT 
        "dazn_user_id"
        ,MAX("quiz_submission_date") OVER (PARTITION BY "dazn_user_id") AS "universal_quiz_submission_date"
        ,"quiz_id"
        ,"quiz_submission_date"
        ,"current_quiz_points"
        ,"category_name"
        ,"quiz_submission_time"
        ,SUM("current_quiz_points") OVER (PARTITION BY "dazn_user_id" ORDER BY "quiz_submission_date" ROWS UNBOUNDED PRECEDING) AS "category_quiz_points"
    FROM picks_source_data_raw
)

,quiz_result_attribution_data AS (
    SELECT 
        "quiz_id"
        ,CASE 
            WHEN DAYOFWEEK(MAX("quiz_submission_date")) = 1 THEN DATEADD('WEEK', 1, MAX("quiz_submission_date"))::DATE
            ELSE DATE_TRUNC('WEEK', DATEADD('WEEK', 1, MAX("quiz_submission_date")))::DATE 
        END AS "quiz_result_attribution_date"
    FROM picks_source_data
    GROUP BY ALL
)

,quiz_with_result_attribution_data AS (
    SELECT *
        ,DATE_TRUNC('WEEK', "quiz_result_attribution_date") AS "quiz_result_attribution_week"
        ,DATE_TRUNC('MONTH', "quiz_result_attribution_date") AS "quiz_result_attribution_month"
    FROM picks_source_data
    LEFT JOIN quiz_result_attribution_data
        USING ("quiz_id")
)

,result_attribution_week_data AS (
    SELECT 
        "dazn_user_id"
        ,"quiz_result_attribution_week"
        ,COUNT(DISTINCT "quiz_id") AS "current_week_cnt" 
        ,SUM("current_quiz_points") AS "current_week_points" 
    FROM quiz_with_result_attribution_data
    GROUP BY ALL 
)

,result_attribution_month_data AS (
    SELECT 
        "dazn_user_id"
        ,"quiz_result_attribution_month"
        ,COUNT(DISTINCT "quiz_id") AS "current_month_cnt" 
        ,SUM("current_quiz_points") AS "current_month_points" 
    FROM quiz_with_result_attribution_data
    GROUP BY ALL 
)

,result_attribution_previous_month_data AS (
    SELECT 
        a."dazn_user_id"
        ,a."quiz_result_attribution_month"
        ,a."current_month_cnt"
        ,a."current_month_points"
        ,COALESCE(b."current_month_cnt", 0) AS "previous_month_cnt"
    FROM result_attribution_month_data AS a  
    LEFT JOIN result_attribution_month_data AS b 
        ON a."dazn_user_id" = b."dazn_user_id" 
            AND b."quiz_result_attribution_month" = DATEADD('MONTH', -1, a."quiz_result_attribution_month")
    GROUP BY ALL 
)

,recent_month_counts AS (
    SELECT * FROM result_attribution_previous_month_data
    QUALIFY ROW_NUMBER() OVER (PARTITION BY "dazn_user_id" ORDER BY "quiz_result_attribution_month" DESC) = 1
)

,user_engagement AS (
    SELECT
        "dazn_user_id"
        ,"category_name"
        ,COUNT(DISTINCT "quiz_id") AS "cnt_quiz_played_total"
        ,COUNT(DISTINCT (
                        CASE
                            WHEN "quiz_submission_date" >= TO_DATE('{{ var('data_interval_start_date') }}', 'YYYY-MM-DD')
                                AND "quiz_submission_date" < TO_DATE('{{ var('data_interval_end_date') }}', 'YYYY-MM-DD') 
                            THEN "quiz_id"
                        END
                        )
        ) AS "cnt_quiz_played_current_week"
        ,CASE
            WHEN "cnt_quiz_played_current_week" > 0 THEN 'PL'
            WHEN "cnt_quiz_played_current_week" = 0 AND "cnt_quiz_played_total" > 0 THEN 'MP'
            ELSE 'NP'
        END AS "engagement_status"
    FROM picks_source_data
    GROUP BY ALL
)

,before_final AS (
    SELECT 
        * 
        ,MAX("quiz_result_attribution_date") OVER () AS "recent_quiz_attribution_date"
    FROM quiz_with_result_attribution_data
    LEFT JOIN recent_month_counts
        USING("dazn_user_id", "quiz_result_attribution_month")
    LEFT JOIN user_engagement
        USING("dazn_user_id", "category_name")
    LEFT JOIN result_attribution_week_data
        USING("dazn_user_id", "quiz_result_attribution_week")    
    QUALIFY ROW_NUMBER() OVER (PARTITION BY quiz_with_result_attribution_data."dazn_user_id" ORDER BY quiz_with_result_attribution_data."quiz_submission_date" DESC) = 1
)

,final AS (
    SELECT *
        ,"universal_quiz_submission_date" >= DATEADD('DAY', -7, "recent_quiz_attribution_date") AS "universal_submission"
        ,RANK() OVER (ORDER BY "category_quiz_points" DESC) AS "category_quiz_points_ranking"
        ,RANK() OVER (ORDER BY "current_month_points" DESC) AS "category_month_quiz_points_ranking"
    FROM before_final 
)

SELECT 
    "dazn_user_id" AS "dazn_user_id"
    ,"category_name" AS "category_name"
    ,"universal_quiz_submission_date" AS "universal_quiz_submission_date"
    ,"category_quiz_points" AS "category_points"
    ,"current_month_cnt" AS "current_month_engagment_count"
    ,"current_month_points" AS "monthly_points"
    ,"previous_month_cnt" AS "last_month_engagment_count"
    ,"cnt_quiz_played_total" AS "total_engagement_count"
    ,"engagement_status" AS "engagement_status"
    ,"current_week_points" AS "weekly_points"
    ,"universal_submission" AS "universal_submission"
    ,"category_quiz_points_ranking" AS "category_ranking"
    ,"category_month_quiz_points_ranking" AS "monthly_ranking"
    ,CURRENT_TIMESTAMP() AS "record_updated_timestamp"
FROM final
