version: 2

models:
  - name: optimove_picks
    columns:
      - name: quiz_id
        description: "This uniquely identifies each quiz in the system."
        quote: true

      - name: dazn_user_id
        description: "This uniquely identifies each user within the DAZN platform."
        quote: true

      - name: current_quiz_points
        description: "Gives the points scored during the quiz."
        quote: true

      - name: category_quiz_points
        description: "Gives the points scored during the quiz of a specific category"
        quote: true

      - name: category_name
        description: "Gives the name of the category."
        quote: true

      - name: quiz_submission_date
        description: "The latest (last) date when the user played quiz for the specific category"
        quote: true

      - name: universal_quiz_submission_date
        description: "The latest (last) date when the user played quiz across all the categories"
        quote: true

      - name: category_points
        description: "Gives the points of the category."
        quote: true

      - name: current_month_engagment_count
        description: "Gives the engagment count of the month."
        quote: true

      - name: monthly_points
        description: "Gives the monthly points."
        quote: true

      - name: last_month_engagment_count
        description: "Gives the engagment count of last month."
        quote: true

      - name: total_engagement_count
        description: "Gives the engagment count of total."
        quote: true

      - name: engagement_status
        description: "Gives the engagment status."
        quote: true

      - name: weekly_points
        description: "Gives the weelky points."
        quote: true

      - name: universal_submission
        description: "Gives the universal submission."
        quote: true

      - name: category_ranking
        description: "Gives the category ranking."
        quote: true

      - name: monthly_ranking
        description: "Gives the monthly ranking."
        quote: true

      - name: record_updated_timestamp
        description: "When this record was last updated"
        quote: true
