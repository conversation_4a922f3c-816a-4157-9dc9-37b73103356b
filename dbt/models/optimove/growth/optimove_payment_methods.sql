{{
    config(
        materialized='table',
        transient=false,
        database='EXCHANGE__OPTIMOVE__OUT__' + snowflake_env(),
        schema='GROWTH',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
        alias='payment_methods',
        tags=['exchange-optimove']
    )
}}

WITH payment_method AS (
    SELECT * FROM TRANSFORMATION_PROD.STAGING.STAGING__PAYMENT_METHOD_CURRENT
    WHERE "payment_method_id" IS NOT NULL
--                  {{ source('STAGING__ZUORA','STAGING__ZUORA__PAYMENT_METHOD_CURRENT') }}
)

,account AS (
    SELECT * FROM {{ source('STAGING__ZUORA','STAGING__ZUORA__ACCOUNT_CURRENT') }}
)

,final AS (
    SELECT
          payment_method."payment_method_id" AS "payment_method_id"
        , payment_method."billing_account_id" AS "billing_account_id"
        , payment_method."payment_method_created_timestamp" AS "created_date"
        , payment_method."payment_method_type" AS "type"
        , payment_method."payment_method_actual_payment_method" AS "actual_payment_method"
        , payment_method."payment_method_credit_card_type" AS "credit_card_type"
        , payment_method."payment_method_last_transaction_timestamp" AS "last_transaction_date"
        , LAST_DAY(CONCAT(payment_method."payment_method_credit_card_expiration_year", '-', payment_method."payment_method_credit_card_expiration_month", '-01')::DATE) AS "expiry_date"
        , account."billing_account_id" IS NOT NULL AS "is_default"
        , COALESCE(payment_method."payment_method_actual_payment_method", payment_method."payment_method_type") AS "payment_method_type"
        , COALESCE(payment_method."payment_method_credit_card_type", payment_method."payment_method_paypal_type", payment_method."payment_method_actual_payment_method") AS "payment_method"
        , payment_method."payment_method_last_failed_sale_transaction_timestamp" AS "last_failed_sale_transaction_date"
        , payment_method."payment_method_last_transaction_status" AS "last_transaction_status"
        , payment_method."payment_method_num_consecutive_failures" AS "num_consecutive_failures"
        , payment_method."payment_method_payment_method_status" AS "payment_method_status"
        , payment_method."payment_method_credit_card_mask_number" AS "payment_method_credit_card_mask_number"
        ,CURRENT_TIMESTAMP()  AS "record_updated_timestamp"
    FROM payment_method
        LEFT JOIN account
    ON payment_method."payment_method_id" = account."default_payment_method_id"
    GROUP BY ALL
)

SELECT * FROM final
