{{
    config(
        materialized='table',
        pre_hook='USE ROLE EXCHANGE__PTS__' + snowflake_env() + '__PII_ROLE',
        post_hook='USE ROLE TRANSFORMATION_' + snowflake_env() + '_ROLE',
        database='EXCHANGE__ELF__OUT__' + snowflake_env(),
        schema='BASE',
        snowflake_warehouse='EXCHANGE_' + snowflake_env() + '_XS_WH',
        tags=['exchange-elf'],
        alias='customers'
    )
}}

WITH user_events_users AS (
    SELECT * FROM {{ source('STAGING_SEGMENT_USERS','STAGING__SEGMENT__USER_EVENTS_USERS') }}
)

,region_dim AS (
    SELECT * FROM {{ source('PRESENTATION','REGION_DIMENSION') }}
)

,user_preferences_users AS (
    SELECT * FROM {{ source('STAGING_SEGMENT_USERS','STAGING__SEGMENT__USER_PREFERENCES_USER_PREFERENCES_CURRENT') }}
    WHERE TRUE
        AND TRUE IN ("elf_offers_email","elf_offers_sms","elf_offers_push")
)

,final AS (
    SELECT
        user_events_users."dazn_user_id" AS "dazn_user_id"
        ,user_events_users."email" AS "email"
        ,user_events_users."first_name" AS "first_name"
        ,user_events_users."last_name" AS "last_name"
        ,user_events_users."product_status_tennis_tv" AS "product_status_elf"
        ,region_dim."country" AS "country"
        ,user_preferences_users."elf_offers_email" AS "elf_offers_email"
        ,user_preferences_users."elf_offers_sms" AS "elf_offers_sms"
        ,user_preferences_users."elf_offers_push" AS "elf_offers_push"
        ,CURRENT_TIMESTAMP() AS "record_updated_timestamp"
    FROM user_events_users
    INNER JOIN user_preferences_users
        ON user_preferences_users."dazn_user_id" = user_events_users."dazn_user_id"
    LEFT JOIN region_dim
        ON user_events_users."home_country_code" = region_dim."join_key"
)

SELECT * FROM final
