{{
    config(
        materialized='table',
        database='EXCHANGE__ELF__OUT__' + snowflake_env(),
        schema='BASE',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
        tags=['exchange-elf'],
        alias='subscriptions'
    )
}}


WITH sub_name_scd AS (
    SELECT 
        *
    FROM {{ source_env( 'SUBSCRIPTION_DOMAIN','SUBSCRIPTION_NAME__SCD' ) }}
    WHERE TRUE
        AND "subscription_product_group" = 'ELF'
        AND "subscription_name_original_created_timestamp"::DATE <= TO_DATE('{{ var('data_interval_end_date') }}', 'YYYY-MM-DD')
        AND COALESCE("record_valid_until_timestamp"::DATE,'9999-12-31') >= TO_DATE('{{ var('data_interval_start_date') }}', 'YYYY-MM-DD')
        QUALIFY TRUE
       AND ROW_NUMBER() OVER (PARTITION BY "subscription_name" ORDER BY "record_valid_from_timestamp" DESC) = 1
)

,final AS (
    SELECT
        sub_name_scd."subscription_name" AS "subscription_name"
        ,sub_name_scd."dazn_user_id" AS "dazn_user_id" 
        ,sub_name_scd."subscription_name_original_created_timestamp"::DATE AS "subscription_start_date"
        ,sub_name_scd."record_valid_until_timestamp"::DATE AS "subscription_end_date"
        ,sub_name_scd."subscription_product_group" AS "subscription_product_group"
        ,sub_name_scd."subscription_type" AS "subscription_type"
        ,sub_name_scd."subscription_country" AS "subscription_country"
        ,CURRENT_TIMESTAMP() AS "record_updated_timestamp"
    FROM sub_name_scd
)

SELECT * FROM final
