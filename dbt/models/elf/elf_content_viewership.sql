{{
    config(
        materialized='table',
        database='EXCHANGE__ELF__OUT__' + snowflake_env(),
        schema='BASE',
        snowflake_warehouse='TRANSFORMATION_' + snowflake_env() + '_XS_WH',
        tags=['exchange-elf'],
        alias='content_viewership'
    )
}}

WITH playback_stream_fact AS (
    SELECT * FROM {{ source('FACT','PLAYBACK_STREAM__FACT') }}
)

,sub_name_scd AS (
    SELECT * FROM {{ source_env( 'SUBSCRIPTION_DOMAIN','SUBSCRIPTION_NAME__SCD' ) }}
    QUALIFY TRUE
        AND ROW_NUMBER() OVER (PARTITION BY "subscription_name" ORDER BY "record_valid_from_timestamp" DESC) = 1
)

,content_item_dim AS (
    SELECT * FROM {{ source('CONTENT','CONTENT_ITEM__DIM') }}
)

,gmr_article AS (
     SELECT "article_id","is_content_linear","competition_title_localised" FROM {{ source('CONTENT_DIMENSION','GMR_ARTICLE__DIM') }}
 )

,playback_stream_location_dim AS (
    SELECT * FROM {{ source('GLOBAL','PLAYBACK_STREAM_LOCATION__DIM') }}
)

,playback_stream_details_dim AS (
    SELECT * FROM {{ source('PLAYBACK_STREAM','PLAYBACK_STREAM_DETAILS__DIM') }}
)

,device_info_dim  AS (
    SELECT * FROM {{ source('OPERATIONS','DEVICE_INFO__DIM') }}
)

,elf_subscriptions AS (
    SELECT "dazn_user_id" 
    FROM 
        sub_name_scd
    WHERE TRUE
        AND "dazn_user_id" IS NOT NULL
        AND "subscription_product_group" = 'ELF'
        AND COALESCE("record_valid_until_timestamp"::DATE,'9999-12-31') >= TO_DATE('{{ var('data_interval_start_date') }}', 'YYYY-MM-DD')
        AND "subscription_name_original_created_timestamp"::DATE < TO_DATE('{{ var('data_interval_end_date') }}', 'YYYY-MM-DD')
)

,customer_identity_dim AS (
	SELECT  COALESCE(cust."viewer_id",cust2."viewer_id") "viewer_id",
    COALESCE(cust."dazn_user_id",cust2."dazn_user_id") "dazn_user_id"
	FROM
	elf_subscriptions a
	LEFT JOIN TRANSFORMATION_PROD.PRESENTATION."CUSTOMER_IDENTITY_DIM_CURRENT" cust ON a."dazn_user_id" = cust."dazn_user_id"
    LEFT JOIN TRANSFORMATION_PROD.PRESENTATION."CUSTOMER_IDENTITY_DIM_CURRENT" cust2 ON a."dazn_user_id" = cust2."crm_account_id"
)

,final AS (
    SELECT
        playback_stream_fact."playback_stream_date" AS "playback_stream_date"
        -- ,customer_identity_dim."dazn_user_id" AS "dazn_user_id"
        ,content_item_dim."competition_name" AS "competition_name"
        ,content_item_dim."fixture_name" AS "fixture_name"
        ,playback_stream_location_dim."playback_stream_country" AS "country"
        ,device_info_dim."device_hardware_type" AS "device_type"
        ,COALESCE(SUM(playback_stream_fact."playback_duration_milliseconds"/3600000), 0) AS "total_playback_time"
        ,COUNT_IF(playback_stream_details_dim."is_engaged_play" = '1') AS "total_streams"
        ,COUNT(DISTINCT CASE WHEN playback_stream_details_dim."is_engaged_play" = '1' THEN playback_stream_fact."viewer_id" END) AS "unique_viewers"
        ,CURRENT_TIMESTAMP() AS "record_updated_timestamp"
    FROM playback_stream_fact
    INNER JOIN customer_identity_dim
        USING ( "viewer_id" )
    LEFT JOIN content_item_dim
        USING ("content_item__skey")
    LEFT JOIN gmr_article
         USING ("article_id")
    LEFT JOIN playback_stream_location_dim
        USING ("playback_stream_location__skey")
    LEFT JOIN playback_stream_details_dim
        USING ("playback_stream_details__skey")
    LEFT JOIN device_info_dim
        USING ("device_info__skey")
    WHERE TRUE
        AND playback_stream_fact."playback_stream_date" >= TO_DATE('{{ var('data_interval_start_date') }}', 'YYYY-MM-DD')
        AND playback_stream_fact."playback_stream_date" < TO_DATE('{{ var('data_interval_end_date') }}', 'YYYY-MM-DD')
    GROUP BY ALL
)

SELECT * FROM final
