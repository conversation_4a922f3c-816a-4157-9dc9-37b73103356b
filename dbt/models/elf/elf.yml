version: 2

models:
  - name: elf_content_viewership
    description: "content viewership reports of ELF tour"
    columns:
      - name: playback_stream_date
        description: "Playback stream date"
        quote: true

      - name: competition_name
        description: "Name of the competition associated with the fixture."
        quote: true

      - name: fixture_name
        description: "The name or description of the fixture associated with the content item."
        quote: true

      - name: country
        description: "Name of the country corresponding to the IP address from which the stream originates."
        quote: true

      - name: device_type
        description: "The primary hardware type of the device e.g. tablet, mobile phone."
        quote: true

      - name: total_playback_time
        description: "Total Playing time hours"
        quote: true

      - name: total_streams
        description: "Total number of streams"
        quote: true

      - name: unique_viewers
        description: "Total number of users engaged"
        quote: true

      - name: record_updated_timestamp
        description: "When this record was last updated"
        quote: true

  - name: elf_subscriptions
    description: "subscriptions details of ELF"
    columns:
      - name: subscription_name
        description: "The Name of the Subscription E.g. A-S15283950."
        quote: true

      - name: dazn_user_id
        description: "User_id of the user"
        quote: true

      - name: subscription_start_date
        description: "Subscription start date"
        quote: true

      - name: subscription_end_date
        description: "Subscription end date"
        quote: true

      - name: subscription_product_group
        description: "ELF product group"
        quote: true

      - name: subscription_type
        description: "A derived field from the term type and the billing period, or external giftcodes, to categorise the subscription type, can be Monthly, Annual, Instalment or Externally Billed"
        quote: true

      - name: subscription_country
        description: "The country of the subscription, originating from the Zuora contact. E.g. Spain, Germany, Japan, United States, United Kingdom, ..."
        quote: true

      - name: record_updated_timestamp
        description: "When this record was last updated"
        quote: true

  - name: elf_customers
    description: "user details of ELF"
    columns:
      - name: dazn_user_id
        description: "Unique ID of the user with in DAZN/Guest user"
        quote: true

      - name: email
        description: "email of user."
        quote: true

      - name: first_name
        description: "first name of user."
        quote: true

      - name: last_name
        description: "last name of user"
        quote: true

      - name: country
        description: "country"
        quote: true

      - name: elf_offers_email
        description: "TBC"
        quote: true

      - name: elf_offers_sms
        description: "TBC"
        quote: true

      - name: elf_offers_push
        description: "TBC"
        quote: true

      - name: record_updated_timestamp
        description: "When this record was last updated"
        quote: true
