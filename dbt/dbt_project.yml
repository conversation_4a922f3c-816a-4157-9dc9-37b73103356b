# dbt Project for Exchange Layer of Data Warehouse
name: 'exchange'
version: '1.0.0'
config-version: 2

profile: 'exchange'

# These configurations specify where dbt should look for different types of files.
# The `model-paths` config, for example, states that models in this project can be
# found in the "models/" directory. You probably won't need to change these!
model-paths: ["models"]
analysis-paths: ["analyses"]
test-paths: ["tests"]
seed-paths: ["seeds"]
macro-paths: ["macros"]
snapshot-paths: ["snapshots"]

target-path: "target"  # directory which will store compiled SQL files
clean-targets:         # directories to be removed by `dbt clean`
  - "target"
  - "dbt_packages"


# Snowflake Config
quoting:
  database: false
  schema: false
  identifier: false
query-comment:
  append: true


# Models Config
# Full documentation: https://docs.getdbt.com/docs/configuring-models
models:
  exchange:
    +persist_docs:
      relation: true
      columns: true
    +materialized: view
    +transient: false


tests:
  +severity: error

# Variable config
vars:
  batch_date: '2025-05-18'
  data_interval_start: '2024-06-25T04:00:00+00:00'
  data_interval_end: '2024-07-02T04:00:00+00:00'
  data_interval_start_date: '2024-06-25'
  data_interval_end_date: '2024-07-02'
  build_mode: 'daily'
  rebuild_days: 1
