module "label_elf_s3" {
  source      = "github.com/getndazn/dazn-dp20-terraform-module-tag-label.git?ref=v2.0.1"
  application = "elf"
  component   = ["exchange"]
  environment = var.environment
  owner       = "<EMAIL>"
  gitrepo     = "github.com/getndazn/dazn-dp20-exchange"
}

module "elf_s3_bucket" {
  source                       = "github.com/getndazn/dazn-terraform-aws-module-data-s3-bucket.git//aws/modules/s3?ref=v2.2.0"
  bucket_name                  = module.label_elf_s3.computed_bucket_name
  tags                         = module.label_elf_s3.tags
  lifecycle_expiration_enabled = false
  versioning                   = true
}
