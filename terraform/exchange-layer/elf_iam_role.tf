resource "aws_iam_role" "elf_iam_role_to_write_into_elf_s3" {
  name = "elf_exchange_iam_role"
  description ="IAM Role to Write data into ELF S3 bucket"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Service = "lambda.amazonaws.com"
        },
        Action = "sts:AssumeRole"
      }
    ]
  })
}

resource "aws_iam_policy" "elf_s3_write_policy" {
  name        = "elf-write-into-s3"
  description = "Allow write access to ELF S3 bucket"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "s3:PutObject"
        ],
        Resource = "arn:aws:s3:::elf-dazn-reports/*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "elf_attach_write_policy" {
  role       = aws_iam_role.elf_iam_role_to_write_into_elf_s3.name
  policy_arn = aws_iam_policy.elf_s3_write_policy.arn
}