from helpers.edmdag import EDMDAG
from dazn_airflow_dbt.task_generator.dbt_task_generator import DbtTaskGenerator
from astronomer.providers.core.sensors.external_task import ExternalTaskSensorAsync
from airflow.utils.state import State
import json
from datetime import datetime, timedelta
from helpers.snowflake_operator import EDMSnowflakeOperator
from helpers.lambda_operator import LambdaOperator


default_args = {
    'profiles_dir': '/usr/local/airflow/dbt/exchange',
    'project_dir': '/usr/local/airflow/dbt/exchange'
}

with EDMDAG(
    dag_id="exchange-picks",
    partner='exchange-picks',
    default_args=default_args,
    start_date=datetime(2024, 6, 24),
    schedule_interval="05 18 * * 1",
    owner='t-rex',
    tags=['exchange', 'picks', 'optimove']
) as dag:
    # TODO: Need to add this functionality to dazn-airflow-dbt package
    with open('dbt/exchange/target/manifest.json') as json_file:
        manifest_data = json.load(json_file)

    dbt_task_generator = DbtTaskGenerator(manifest_data)
    dbt_task_generator.generate_dbt_tasks()

    check_staging_picks_success = ExternalTaskSensorAsync(
        task_id=f'check-staging-picks-success',
        external_dag_id='staging-picks',
        external_task_id='staging-picks-success-marker-exchange-picks',
        failed_states=[
            State.FAILED,
            State.UPSTREAM_FAILED
        ],
        execution_delta=timedelta(minutes=3),
        mode='reschedule',
        poke_interval=60 * 10,
        timeout=60 * 60 * 8,
        retries=0
    )

    check_staging_picks_success >> dbt_task_generator.roots

    leaves = dbt_task_generator.leaves

    japan_picks_export = EDMSnowflakeOperator(
        task_id='japan_picks_export',
        sql='sql/exchange/unload/picks/dazn_picks_jp.sql',
        dataset='JapanPicks',
        database='EXCHANGE__OPTIMOVE__OUT',
        schema='ENGAGEMENT',
        params={
            'table': 'PICKS'
        }
    )

    send_to_sftp_optimove = LambdaOperator(
        task_id=f"send_to_sftp_optimove",
        function_name="data_sftp_optimove_picks_lambda",
        invocation_type='RequestResponse',
        payload={
            'dataset': "JapanPicks",
            'batch_date': f"{dag.year}-{dag.month}-{dag.day}"
        }
    )
    leaves >> japan_picks_export >> send_to_sftp_optimove


