import json
from datetime import datetime, timedelta
from astronomer.providers.core.sensors.external_task import ExternalTaskSensorAsync
from airflow.utils.state import State
from dazn_airflow_dbt.task_generator.dbt_task_generator import DbtTaskGenerator
from helpers.edmdag import EDMDAG

from helpers.lambda_operator import LambdaOperator
from helpers.snowflake_operator import EDMSnowflakeOperator
from airflow.providers.amazon.aws.hooks.base_aws import AwsBaseHook

default_args = {
    'profiles_dir': '/usr/local/airflow/dbt/exchange',
    'project_dir': '/usr/local/airflow/dbt/exchange'
}

with EDMDAG(
    dag_id="exchange-elf",
    partner='elf',
    default_args=default_args,
    start_date=datetime(2025, 4, 28),
    schedule_interval="30 7 * * *",  # TBC
    owner='t-rex',
    tags=['exchange','elf']
) as dag:


    if dag.env=='dev':
        aws_profile='075583736417'
    elif dag.env=='prod':
        aws_profile='406168740187'
    else:
        aws_profile='410489415128'

    passphrase_path = '/dazn/dp2/elf_exchange/other_params'

    def get_parameter_from_ssm(parameter_name):
        # Create an AWSBaseHook
        aws_hook = AwsBaseHook(client_type='ssm', region_name='eu-central-1')
        # Get the SSM client
        ssm_client = aws_hook.get_client_type()
        # Retrieve the parameter value
        response = ssm_client.get_parameter(Name=parameter_name, WithDecryption=True)
        parameter_value_str = response['Parameter']['Value']
        other_params=json.loads(parameter_value_str)
        pass_phrase=other_params['pass_phrase']

        return pass_phrase

    # TODO: Need to add this functionality to dazn-airflow-dbt package
    with open('dbt/exchange/target/manifest.json') as json_file:
        manifest_data = json.load(json_file)

    dbt_task_generator = DbtTaskGenerator(manifest_data)
    dbt_task_generator.generate_dbt_tasks()

    check_presentation_subscription_domain_success = ExternalTaskSensorAsync(
        task_id='check-presentation-subscription-domain-success',
        external_dag_id='presentation-subscription-domain',
        external_task_id='presentation-subscription-domain-success-marker-elf',
        failed_states=[
            State.FAILED,
            State.UPSTREAM_FAILED
        ],
        execution_delta=timedelta(hours=6, minutes=40),
        mode='reschedule',
        poke_interval=60 * 10,
        timeout=60 * 60 * 8,
        retries=0
    )

    check_playback_stream_mart_success = ExternalTaskSensorAsync(
        task_id='check-presentation-playback-stream-mart-success',
        external_dag_id='presentation-playback-stream-mart',
        external_task_id='presentation-playback-stream-mart-success-marker-elf',
        failed_states=[
            State.FAILED,
            State.UPSTREAM_FAILED
        ],
        execution_delta=timedelta(hours=5, minutes=30),
        mode='reschedule',
        poke_interval=60 * 5,
        timeout=60 * 60 * 8,
        retries=0
    )
    #segment data for customers model
    check_presentation_staging_success = ExternalTaskSensorAsync(
        task_id='check-presentation-staging-success',
        external_dag_id='presentation-staging',
        external_task_id='presentation-staging-success-marker-elf',
        failed_states=[
            State.FAILED,
            State.UPSTREAM_FAILED
        ],
        execution_delta=timedelta(hours=6, minutes=40),
        mode='reschedule',
        poke_interval=60 * 5,
        timeout=60 * 60 * 8,
        retries=0
    )

    check_presentation_staging_success >> dbt_task_generator.get_task_by_id('run-elf_customers')
    check_presentation_subscription_domain_success >> dbt_task_generator.get_task_by_id('run-elf_subscriptions')
    check_playback_stream_mart_success >> dbt_task_generator.get_task_by_id('run-elf_content_viewership')

    leaves = dbt_task_generator.leaves

    # content_viewership
    elf_content_viewership_export = EDMSnowflakeOperator(
        task_id='elf_content_viewership_export',
        sql='sql/exchange/unload/elf/elf_weekly_export.sql',
        dataset='content_viewership',
        database='EXCHANGE__ELF__OUT',
        schema='BASE',
        params={
            'table': 'CONTENT_VIEWERSHIP'
        }
    )
    leaves >> elf_content_viewership_export

    elf_content_viewership_audit = EDMSnowflakeOperator(
        task_id='elf_content_viewership_audit',
        sql='sql/exchange/unload/elf/elf_audit.sql',
        dataset='content_viewership',
        database='EXCHANGE__ELF__OUT',
        schema='BASE',
        params={
            'table': 'CONTENT_VIEWERSHIP',
            'stage': 'EXCHANGE__ELF__AUDIT__ST',
            'external_table': 'CONTENT_VIEWERSHIP_AUDIT'
        }
    )

    elf_content_viewership_unload = LambdaOperator(
        task_id=f"elf_content_viewership_unload",
        function_name=f"data_partner-exchange_s3_{dag.env}",
        invocation_type='RequestResponse',
        payload={
            "partner": "elf",
            "source_bucket": f"dazn-{dag.env}-elf-exchange", 
            "target_bucket": "elf-dazn-reports", 
            "s3_file_path": f"content_viewership/{{{{ (data_interval_end - macros.timedelta(days=1)) | ds_nodash }}}}/content_viewership",
            "dataset": "content_viewership", 
            "pii": "False", 
            "batch_date": f"{{{{ data_interval_end | ds_nodash }}}}",
            "iam_role": f"arn:aws:iam::{aws_profile}:role/elf_exchange_iam_role"
        }
    )

    elf_content_viewership_export >> elf_content_viewership_audit >> elf_content_viewership_unload

    # subscriptions
    elf_subscriptions_export = EDMSnowflakeOperator(
        task_id='elf_subscriptions_export',
        sql='sql/exchange/unload/elf/elf_weekly_export.sql',
        dataset='subscriptions',
        database='EXCHANGE__ELF__OUT',
        schema='BASE',
        params={
            'table': 'SUBSCRIPTIONS'
        }
    )
    leaves >> elf_subscriptions_export

    elf_subscriptions_audit = EDMSnowflakeOperator(
        task_id='elf_subscriptions_audit',
        sql='sql/exchange/unload/elf/elf_audit.sql',
        dataset='subscriptions',
        database='EXCHANGE__ELF__OUT',
        schema='BASE',
        params={
            'table': 'SUBSCRIPTIONS',
            'stage': 'EXCHANGE__ELF__AUDIT__ST',
            'external_table': 'SUBSCRIPTIONS_AUDIT'
        }
    )

    elf_subscriptions_unload = LambdaOperator(
        task_id=f"elf_subscriptions_unload",
        function_name=f"data_partner-exchange_s3_{dag.env}",
        invocation_type='RequestResponse',
        payload={
            "partner": "elf",
            "source_bucket": f"dazn-{dag.env}-elf-exchange", 
            "target_bucket": "elf-dazn-reports", 
            "s3_file_path": f"subscriptions/{{{{ (data_interval_end - macros.timedelta(days=1)) | ds_nodash }}}}/subscriptions",
            "dataset": "subscriptions", 
            "pii": "False", 
            "batch_date": f"{{{{ data_interval_end | ds_nodash }}}}",
            "iam_role": f"arn:aws:iam::{aws_profile}:role/elf_exchange_iam_role"
        }
    )

    elf_subscriptions_export >> elf_subscriptions_audit >> elf_subscriptions_unload

    #customers
    elf_customer_export = EDMSnowflakeOperator(
        task_id='elf_customer_export',
        sql='sql/exchange/unload/elf/elf_weekly_export.sql',
        dataset='customers',
        database='EXCHANGE__ELF__OUT',
        schema='BASE',
        params={
            'table': 'CUSTOMERS',
            'passphrase' : f"{get_parameter_from_ssm(passphrase_path)}"
        }
    )
    leaves >> elf_customer_export

    elf_customers_audit = EDMSnowflakeOperator(
        task_id='elf_customers_audit',
        sql='sql/exchange/unload/elf/elf_audit.sql',
        dataset='customers',
        database='EXCHANGE__ELF__OUT',
        schema='BASE',
        params={
            'table': 'CUSTOMERS',
            'stage': 'EXCHANGE__ELF__AUDIT__ST',
            'external_table': 'CUSTOMERS_AUDIT',
            'passphrase' : f"{get_parameter_from_ssm(passphrase_path)}"
        }
    )

    elf_customers_unload = LambdaOperator(
        task_id=f"elf_customers_unload",
        function_name=f"data_partner-exchange_s3_{dag.env}",
        invocation_type='RequestResponse',
        payload={
            "partner": "elf",
            "source_bucket": f"dazn-{dag.env}-elf-exchange", 
            "target_bucket": "elf-dazn-reports", 
            "s3_file_path": f"customers/{{{{ (data_interval_end - macros.timedelta(days=1)) | ds_nodash }}}}/customers",
            "dataset": "customers", 
            "pii": "True", 
            "batch_date": f"{{{{ data_interval_end | ds_nodash }}}}",
            "iam_role": f"arn:aws:iam::{aws_profile}:role/elf_exchange_iam_role"
        }
    )

    elf_customer_export >> elf_customers_audit >> elf_customers_unload
