"""
### Exchange Nielsen DACH Pipeline

This pipeline is used to send files to Nielsen for DACH Distinct IP address counts and Active content at intervals.

"""
import json
from datetime import datetime, timedelta

from astronomer.providers.core.sensors.external_task import ExternalTaskSensorAsync
from airflow.utils.state import State
from dazn_airflow_dbt.task_generator.dbt_task_generator import DbtTaskGenerator
from helpers.edmdag import EDMDAG
from helpers.snowflake_operator import EDMSnowflakeOperator

default_args = {
    'profiles_dir': '/usr/local/airflow/dbt/exchange',
    'project_dir': '/usr/local/airflow/dbt/exchange'
}

with EDMDAG(
    dag_id="exchange-nielsen-dach",
    partner='exchange-nielsen-dach',
    default_args=default_args,
    start_date=datetime(2023, 5, 5),
    schedule_interval="0 4 * * *",
    max_active_runs=1,
    owner='t-rex',
    tags=[
        'exchange',
    ]
) as dag:

    # TODO: Need to add this functionality to dazn-airflow-dbt package
    with open('dbt/exchange/target/manifest.json') as json_file:
        manifest_data = json.load(json_file)

    dbt_task_generator = DbtTaskGenerator(manifest_data)
    dbt_task_generator.generate_dbt_tasks()

    check_pipeline_plays_mart_success = ExternalTaskSensorAsync(
        task_id='check-presentation-plays-mart-success',
        external_dag_id='presentation-playback-stream-mart',
        external_task_id='presentation-plays-mart-success-marker-nielsen',
        failed_states=[
            State.FAILED,
            State.UPSTREAM_FAILED
        ],
        execution_delta=timedelta(hours=2),
        mode='reschedule',
        poke_interval=60 * 10,
        timeout=60 * 60 * 4,
        retries=0
    )

    check_pipeline_subscription_success = ExternalTaskSensorAsync(
        task_id='check-pipeline-subscription-domain-success',
        external_dag_id='presentation-subscription-domain',
        external_task_id='presentation-subscription-domain-success-marker-nielsen',
        failed_states=[
            State.FAILED,
            State.UPSTREAM_FAILED
        ],
        execution_delta=timedelta(hours=3, minutes=10),
        mode='reschedule',
        poke_interval=60 * 10,
        timeout=60 * 60 * 4,
        retries=0
    )

    check_presentation_customer_id_dim_success = ExternalTaskSensorAsync(
        task_id='check-presentation-customer-id-dim-success',
        external_dag_id='presentation-customer-identity',
        external_task_id='presentation-customer-id-dim-nielsen-success-marker',
        failed_states=[
            State.FAILED,
            State.UPSTREAM_FAILED
        ],
        execution_delta=timedelta(hours=3),
        mode='reschedule',
        poke_interval=60 * 10,
        timeout=60 * 60 * 8,
        retries=0
    )

    roots = dbt_task_generator.roots

    check_pipeline_subscription_success >> roots
    check_pipeline_plays_mart_success >> roots
    check_presentation_customer_id_dim_success >> roots

    leaves = dbt_task_generator.leaves

    datasets = [
        'concurrency',
        'distinct_ips',
    ]

    for dataset in datasets:

        task_id = f"export-nielsen-dach-{dataset.replace('_', '-')}"
        table_name = f"NIELSEN_DACH_{dataset.upper()}"
        export_task = EDMSnowflakeOperator(
            task_id=task_id,
            sql='sql/exchange/unload/nielsen/nielsen_dach_export.sql',
            dataset=dataset,
            database='EXCHANGE__NIELSEN__OUT',
            schema='NIELSEN_DACH',
            params={
                'table': table_name
            }
        )

        task_id = f"export-nielsen-dach-{dataset.replace('_', '-')}-audit"
        audit_task = EDMSnowflakeOperator(
            task_id=task_id,
            sql='sql/exchange/audit/audit.sql',
            dataset=dataset,
            database='EXCHANGE__NIELSEN__OUT',
            schema='NIELSEN_DACH',
            params={
                'table': table_name,
                'stage': 'EXCHANGE__NIELSEN__AUDIT__ST',
                'external_table': f"{table_name}_AUDIT"
            }
        )

        leaves >> export_task >> audit_task
